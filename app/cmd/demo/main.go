package main

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/nickabs/demo/internal/templates"
)

type user struct {
	name  string
	email string
}

type users struct {
	user []user
}


func registerPageHandler(c echo.Context) error {
	component := templates.RegisterPage()
	return component.Render(c.Request().Context(), c.Response().Writer)
}

func register(c echo.Context) error {
	req := c.Request()
	res := c.Response()

	name := req.FormValue("name")
	email := req.FormValue("email")

	user := user{name, email}



	res.Write([]byte(`<button id="done" disabled>done</button>`))

	return nil

}

func (u *users) AddUser(newUser user) {
	u = append(u, newUser)
}
func main() {
	e := echo.New()

	users := make([]user, 0)

	e.Use(middleware.Logger())

	e.GET("/register", registerPageHandler)
	e.POST("/register", register)

	e.Logger.Fatal(e.Start(":8082"))
}
