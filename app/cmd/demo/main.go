package main

import (
	"fmt"
	"sync"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/nickabs/demo/internal/templates"
)

type user struct {
	name  string
	email string
}

type UserStore struct {
	users []user
	mutex sync.RWMutex
}

func (us *UserStore) AddUser(newUser user) {
	us.mutex.Lock()
	defer us.mutex.Unlock()
	us.users = append(us.users, newUser)
}

func (us *UserStore) GetUsers() []user {
	us.mutex.RLock()
	defer us.mutex.RUnlock()
	return us.users
}

func registerPageHandler(c echo.Context) error {
	component := templates.RegisterPage()
	return component.Render(c.Request().Context(), c.Response().Writer)
}

func register(c echo.Context) error {
	// Get the user store from context
	userStore := c.Get("userStore").(*UserStore)
	
	req := c.Request()
	res := c.Response()

	name := req.FormValue("name")
	email := req.FormValue("email")

	// Create new user
	newUser := user{name: name, email: email}
	
	// Add to user store
	userStore.AddUser(newUser)
	
	// Optional: Print current users for debugging
	fmt.Printf("Current users: %+v\n", userStore.GetUsers())

	res.Write([]byte(`<button id="done" disabled>done</button>`))

	return nil
}

func main() {
	e := echo.New()

	// Create user store
	userStore := &UserStore{
		users: make([]user, 0),
	}

	// Middleware to inject user store into context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("userStore", userStore)
			return next(c)
		}
	})

	e.Use(middleware.Logger())

	e.GET("/register", registerPageHandler)
	e.POST("/register", register)

	e.Logger.Fatal(e.Start(":8082"))
}
