package templates

import (
	"github.com/nickabs/demo/internal/store"
)

templ BaseLayout(title string) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title }</title>
			<script src="https://unpkg.com/htmx.org@2.0.4"></script>
			<link href="/static/css/app.css" rel="stylesheet"/>
		</head>
		<body>
			{ children... }	
		</body>
	</html>
}

templ RegisterPage() {
	@BaseLayout("register") {
		<h1>User Registration</h1>
		<form
			hx-post="/register"
			hx-target="#contacts"
			hx-swap="innerHTML"
		>
			<div>
				<label>Name:</label>
				<input type="text" name="name" autocomplete="given-name" required/>
			</div>
			<div>
				<label>Email:</label>
				<input type="email" name="email" autocomplete="email" required/>
			</div>
			<br>
			<button type="submit" id="submit">Submit</button>
		</form>
		<br>
		<button id="clear"
			hx-post="/clear"
			hx-target="#contacts"
			hx-swap="outerHTML"
		>Clear All</button>
		<br>
		<h3>Stored Contacts</h3>
		<div id="contacts">
			<!-- This will be populated by HTMX -->
		</div>

		<style>
			form div { margin: 10px 0; }
			label { display: inline-block; width: 80px; }
			input { padding: 5px; margin-left: 10px; }
			button { padding: 8px 16px; background: #007cba; color: white; border: none; cursor: pointer; margin: 5px; }
			button:hover { background: #005a87; }
			.contacts { margin-top: 20px; }
			.contacts ul { list-style-type: none; padding: 0; }
			.contacts li { padding: 10px; border: 1px solid #ddd; margin: 5px 0; display: flex; align-items: center; }
			.contacts svg { width: 20px; height: 20px; margin-right: 10px; }
		</style>
	}
}

templ RenderStoredContacts(userStore *store.UserStore) {
	<div class="contacts">
	<br> 
	<ul>
		for _,user := range userStore.GetUsers() {
			<li> 
				<svg fill="#000000" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 413.601 413.601" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M395.439,368.206h18.158v45.395h-45.395v-18.158h27.236V368.206z M109.956,413.601h64.569v-18.158h-64.569V413.601z M239.082,413.601h64.558v-18.158h-64.558V413.601z M18.161,368.206H0.003v45.395h45.395v-18.158H18.161V368.206z M18.161,239.079 H0.003v64.562h18.158V239.079z M18.161,109.958H0.003v64.563h18.158V109.958z M0.003,45.395h18.158V18.158h27.237V0H0.003V45.395z M174.519,0h-64.563v18.158h64.563V0z M303.64,0h-64.558v18.158h64.558V0z M368.203,0v18.158h27.236v27.237h18.158V0H368.203z M395.439,303.642h18.158v-64.562h-18.158V303.642z M395.439,174.521h18.158v-64.563h-18.158V174.521z M325.45,93.187 c-11.467-11.464-30.051-11.464-41.518,0l-77.135,77.129l-77.129-77.129c-11.476-11.464-30.056-11.464-41.521,0 c-11.476,11.47-11.476,30.062,0,41.532l77.118,77.123l-77.124,77.124c-11.476,11.479-11.476,30.062,0,41.529 c5.73,5.733,13.243,8.605,20.762,8.605c7.516,0,15.028-2.872,20.765-8.605l77.129-77.124l77.129,77.124 c5.728,5.733,13.246,8.605,20.765,8.605c7.513,0,15.025-2.872,20.759-8.605c11.479-11.467,11.479-30.062,0-41.529l-77.124-77.124 l77.124-77.123C336.923,123.243,336.923,104.656,325.45,93.187z"></path> </g> </g></svg>
				
				<div>{ user.Email } | { user.Name } </div>
			</li>
		}
	</ul>
	</div>

}
