package templates

templ BaseLayout(title string) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title }</title>
			<script src="https://unpkg.com/htmx.org@2.0.4"></script>
			<link href="/static/css/app.css" rel="stylesheet"/>
		</head>
		<body>
			{ children... }	
		</body>
	</html>
}
templ Home(title string) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title }</title>
			<script src="https://unpkg.com/htmx.org@2.0.4"></script>
			<link href="/static/css/app.css" rel="stylesheet"/>
		</head>
		<body>
			
		<h1> { title } </h1>
		
		<button id="count-button"
				hx-post="/count"
				hx-trigger="click"
				hx-target="#count"
				hx-swap="outerHTML"
			>count</button>
		</body>
		<div id="count" />
	</html>
}


templ CountTemplate(count int) {
	<div id="count">Count: { count }</div>
}


templ RegisterPage() {
	@BaseLayout("register") {
		<form
			hx-post="/register"
			hx-target="#output"
			hx-swap="innerHTML"
		> 
			name: <input type="text" name="name" autocomplete="given-name"/>
			email <input type="email" name="email" autocomplete="email"/>

			<button id="submit">submit</button>
		</form>
		<div id="output"/>
	}
}
