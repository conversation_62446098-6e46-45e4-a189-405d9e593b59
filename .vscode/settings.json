{"files.associations": {"*.templ": "templ"}, "emmet.includeLanguages": {"templ": "html"}, "html.customData": [{"version": 1.1, "tags": [], "attributes": [{"name": "hx-get", "description": "Issues a GET request to the given URL"}, {"name": "hx-post", "description": "Issues a POST request to the given URL"}, {"name": "hx-target", "description": "Specifies the target element to be swapped"}, {"name": "hx-swap", "description": "Specifies how the response will be swapped in relative to the target", "values": [{"name": "innerHTML"}, {"name": "outerHTML"}, {"name": "beforebegin"}, {"name": "afterbegin"}, {"name": "beforeend"}, {"name": "afterend"}, {"name": "delete"}, {"name": "none"}]}, {"name": "hx-trigger", "description": "Specifies the event that triggers the request"}, {"name": "hx-indicator", "description": "Specifies the element to show during the request"}]}]}